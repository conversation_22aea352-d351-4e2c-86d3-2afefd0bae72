import os
from datetime import datetime
from zoneinfo import ZoneInfo

from openpyxl import load_workbook
from openpyxl.cell.rich_text import CellRichText, TextBlock
from openpyxl.cell.text import InlineFont
from openpyxl.drawing.image import Image
from openpyxl.styles import Alignment, Border, Font, PatternFill, Side
from openpyxl.utils.dataframe import dataframe_to_rows

from pb_agro_report_rgca_api.enums.sub_modules import SubModules


class ExcelService:
    def save_file_to_excel(self, client_document, merged_df):
        actual_date = datetime.now()
        formatted_date = actual_date.strftime("%Y%m%d%H%M")
        file_name = f"rgca_report_{formatted_date}.xlsx"
        output_local_file = f"media/{client_document}/results"

        if not os.path.exists(output_local_file):
            os.mkdir(output_local_file)

        output_local_file += f"/{file_name}"

        open(output_local_file, "w")

        merged_df.to_excel(
            output_local_file,
            index=False,
            sheet_name="Dad<PERSON>",
            startrow=2,
            startcol=1,
            engine="openpyxl",
        )

        return output_local_file, file_name

    def building_structure_dimensions(self, sheet):
        sheet.insert_rows(3)

        sheet.row_dimensions[1].height = (
            (sheet.row_dimensions[1].height * 4)
            if sheet.row_dimensions[1].height
            else 80
        )

        logo = Image("images/serasa_logo.png")
        sheet.add_image(logo, "B1")
        sheet.sheet_view.showGridLines = False

        for col in sheet.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            adjusted_width = max_length + 2
            sheet.column_dimensions[column].width = adjusted_width

        sheet.row_dimensions[2].height = 30
        sheet.row_dimensions[3].height = 48
        sheet.row_dimensions[4].height = 30

        return sheet

    def get_sheet_font_and_colors(self):
        header_font = Font(name="Roboto", size=11, bold=True, color="FFFFFF")
        module_font = Font(name="Roboto", size=14, bold=True, color="FFFFFF")

        purple_fill = PatternFill(
            start_color="7030A0", end_color="7030A0", fill_type="solid"
        )
        gray_fill = PatternFill(
            start_color="a6a6a6", end_color="a6a6a6", fill_type="solid"
        )
        pink_fill = PatternFill(
            start_color="e80070", end_color="e80070", fill_type="solid"
        )

        black_fill = PatternFill(
            start_color="000000", end_color="000000", fill_type="solid"
        )

        blue_fill = PatternFill(
            start_color="4b67a2", end_color="4b67a2", fill_type="solid"
        )
        green_fill = PatternFill(
            start_color="53c2b8", end_color="53c2b8", fill_type="solid"
        )

        return (
            header_font,
            module_font,
            purple_fill,
            gray_fill,
            pink_fill,
            black_fill,
            blue_fill,
            green_fill,
        )

    def merge_and_style_cells(
        self, sheet, start_row, end_row, start_column, end_column, value, font
    ):
        sheet.merge_cells(
            start_row=start_row,
            start_column=start_column,
            end_row=end_row,
            end_column=end_column,
        )
        cell = sheet.cell(row=start_row, column=start_column, value=value)
        cell.font = font

    def add_module(
        self,
        sheet,
        merged_df,
        start_column,
        end_column,
        module_name,
        header_font,
        module_font,
        module_color,
        columns_to_find=False,
    ):
        if columns_to_find:
            start_column, end_column = self.find_columns(
                sheet, start_column, end_column
            )

        self.merge_and_style_cells(
            sheet=sheet,
            start_row=3,
            end_row=3,
            start_column=start_column,
            end_column=end_column,
            value=module_name,
            font=Font(name="Roboto", size=14),
        )

        self.style_cells(
            sheet,
            merged_df,
            start_column,
            end_column,
            header_font,
            module_font,
            module_color,
        )

    def find_columns(self, sheet, start_column, end_column):
        columns = [start_column, end_column]
        column_indices = {col: None for col in columns}
        for cell in sheet[4]:
            if cell.value in column_indices:
                column_indices[cell.value] = cell.column

        return column_indices[start_column], column_indices[end_column]

    def style_cells(
        self,
        sheet,
        merged_df,
        start_column,
        end_column,
        header_font,
        module_font,
        module_color,
    ):
        for row in sheet.iter_rows(
            min_row=3, max_row=4, min_col=1, max_col=merged_df.shape[1]
        ):
            for i, cell in enumerate(row, start=1):
                cell.alignment = Alignment(horizontal="center", vertical="center")
                if row[0].row == 4:
                    cell.alignment = Alignment(horizontal="center")

                cell.font = module_font if row[0].row == 3 else header_font
                if start_column <= i <= end_column:
                    cell.fill = module_color

    def add_client_module(self, sheet, header_font, module_font, merged_df, gray_fill):
        end_column = 4
        start_column = 2
        if "CAR" not in merged_df.columns:
            end_column = 3
        elif "Documento" not in merged_df.columns:
            end_column = 2

        self.add_module(
            sheet,
            merged_df,
            start_column,
            end_column,
            "CLIENTE",
            header_font,
            module_font,
            gray_fill,
        )

    def format_probability_column(self, sheet, merged_df):
        percent_column_index = (
            merged_df.columns.get_loc("Probabilidade de Inadimplência") + 2
        )

        for row in sheet.iter_rows(
            min_row=4,
            max_row=sheet.max_row,
            min_col=percent_column_index,
            max_col=percent_column_index,
        ):
            for cell in row:
                if isinstance(cell.value, (int, float)):
                    cell.number_format = "0.00%"

    def input_border(self, sheet, merged_df):
        thin_border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin"),
        )

        start_row = 3
        end_row = 4 + len(merged_df)
        start_col = 2
        end_col = start_col + len(merged_df.columns) - 1

        for row in sheet.iter_rows(
            min_row=start_row, max_row=end_row, min_col=start_col, max_col=end_col
        ):
            for cell in row:
                cell.border = thin_border

    def add_disclaimer(self, sheet, merged_df):
        sheet.cell(
            row=len(merged_df) + 7,
            column=2,
            value="DISCLAIMER: A Serasa Experian se responsabiliza "
            "apenas pela integridade das informações "
            "tal como recebida de suas fontes, "
            "não cabendo a esta responsabilidade "
            "pela veracidade das informações ou por decisões que "
            "eventualmente tomem como base as "
            "informações disponibilizadas como insumo decisório.",
        )

        sheet.cell(
            row=len(merged_df) + 8,
            column=2,
            value="As informações apresentadas não consistem em emissão de juízo "
            "de valor pela Serasa Experian, "
            "apenas em análise realizada com base em dados "
            "estritamente objetivos e estatisticamente relevantes.",
        )

    def add_report_title(
        self,
        sheet,
        title_column_letter,
        second_title_column_letter,
        initial_index_column,
        final_merge_index_column,
        image_serasa_width=19,
    ):
        sheet.column_dimensions["B"].width = image_serasa_width
        sheet.column_dimensions[second_title_column_letter].width = 30
        sheet.column_dimensions[title_column_letter].width = 49
        sheet.column_dimensions[second_title_column_letter].width = 30

        title_cell = sheet.cell(
            row=1,
            column=initial_index_column,
            value="Diagnóstico de Carteira",
        )
        title_cell.font = Font(name="Roboto", size=20, bold=True)

        generated_cell = sheet.cell(
            row=1,
            column=initial_index_column + 1,
            value=datetime.now(ZoneInfo("America/Sao_Paulo")).strftime(
                "Relatório gerado em: %d/%m/%Y às %Hh%M"
            ),
        )
        generated_cell.font = Font(name="Roboto", size=8)

        sheet.merge_cells(
            start_row=1,
            start_column=1,
            end_row=1,
            end_column=final_merge_index_column,
        )

    def add_created_at_module(self, sheet, black_fill, merged_df):
        DATA_CONSULTA = "Data da Consulta"

        column_indice = len(merged_df.columns) - 1
        for cell in sheet[4]:
            if cell.value == DATA_CONSULTA:
                column_indice = cell.column

        sheet.merge_cells(
            start_row=3,
            start_column=column_indice,
            end_row=4,
            end_column=column_indice,
        )
        module_created_at_cell = sheet.cell(
            row=3,
            column=column_indice,
            value=DATA_CONSULTA,
        )
        module_created_at_cell.font = Font(name="Roboto", size=11, color="FFFFFF")
        module_created_at_cell.fill = black_fill
        module_created_at_cell.alignment = Alignment(
            horizontal="center", vertical="center"
        )

    def write_data_sheet(self, output_local_file, merged_df, sub_modules):
        workbook = load_workbook(output_local_file)
        sheet = workbook["Dados"]
        sheet = self.building_structure_dimensions(sheet)

        (
            header_font,
            module_font,
            purple_fill,
            gray_fill,
            pink_fill,
            black_fill,
            blue_fill,
            green_fill,
        ) = self.get_sheet_font_and_colors()
        self.add_client_module(sheet, header_font, module_font, merged_df, gray_fill)

        if SubModules.LAND.value in sub_modules and "CAR" in merged_df.columns:
            self.add_module(
                sheet,
                merged_df,
                "Código Município",
                "Retificação CAR",
                "MÓDULO AMBIENTAL",
                header_font,
                module_font,
                blue_fill,
                True,
            )

        if SubModules.AGRICULTURAL.value in sub_modules and "CAR" in merged_df.columns:
            self.add_module(
                sheet,
                merged_df,
                "Soja (2021/22)",
                "Presença de Armazéns",
                "MÓDULO AGRÍCOLA",
                header_font,
                module_font,
                pink_fill,
                True,
            )

        if SubModules.ESG.value in sub_modules and "CAR" in merged_df.columns:
            self.add_module(
                sheet,
                merged_df,
                "Embargos IBAMA (Lista)",
                "PRA",
                "MÓDULO ESG",
                header_font,
                module_font,
                green_fill,
                True,
            )

        if SubModules.CREDIT.value in sub_modules and "Documento" in merged_df.columns:
            self.add_module(
                sheet,
                merged_df,
                "Agro Relacionado",
                "Protestos",
                "MÓDULO CRÉDITO",
                header_font,
                module_font,
                purple_fill,
                True,
            )

            if "Probabilidade de Inadimplência" in merged_df.columns:
                self.format_probability_column(sheet, merged_df)

        if "Documento" in merged_df.columns:
            self.add_report_title(sheet, "D", "E", 4, 3)
        else:
            self.add_report_title(sheet, "C", "D", 3, 2, 45)

        self.add_created_at_module(sheet, black_fill, merged_df)

        self.input_border(sheet, merged_df)
        self.add_disclaimer(sheet, merged_df)

        workbook.save(output_local_file)
        workbook.close()

    def write_notes_sheet(self, output_local_file):
        workbook = load_workbook(output_local_file)
        sheet = workbook.create_sheet("Notas")

        content_font = Font(name="Roboto", size=11, color="000000")

        sheet.sheet_view.showGridLines = False

        title_cell = sheet.cell(row=1, column=2, value="Notas")
        title_cell.font = Font(name="Roboto", size=14, bold=True)
        title_cell.alignment = Alignment(horizontal="center", vertical="center")

        notes = [
            "Agro Relacionado: O diagnóstico da carteira é realizado apenas "
            "para o documentos CPF/CNPJ relacionados ao Agro.",
            "Agro Score: É uma ferramenta de análise de risco de crédito "
            "voltada para pessoas físicas do público agro, variando de  0 a "
            "1000, em que, quanto mais próximo de 1.000, menor é a "
            "probabilidade de inadimplência.",
            "BNDES: Indica se o documento informado (CPF/CNPJ) possui "
            "financiamentos vinculados ao Banco Nacional de Desenvolvimento "
            "Econômico e Social - BNDES.",
            "CPF Ativo: Indica se o documento informado está ativo.",
            "Maior de 21 anos: A partir do documento CPF informado, "
            "indica se a pessoa é maior de 21 anos.",
            "Receita Federal Regular: Comprova a regularidade do documento "
            "informado (CPF/CNPJ) perante a Receita Federal.",
            "CNDT Regular: A Certidão Negativa de Débitos Trabalhistas "
            "comprova a regulariadedo documento perante ao Banco Nacional "
            "de Devedores Trabalhistas.",
            "FGTS Regular: Comprova a regularidade do empregador (CPF/CNPJ "
            "informado) perante ao Fundo de Garantia do Tempo de "
            "Serviço - FGTS.",
            "Sintegra: Informa se o documento informado apresenta alguma "
            "inscrição no Sistema Integrado de Informações sobre Operações "
            "Interestaduais com Mercadorias e Serviços - SINTEGRA.",
            "Protestos: Indica se o documento informado (CPF/CNPJ) possui "
            "protestos em cartórios.",
            "Consulta Indisponível: As consultas podem não estar disponíveis "
            "durante a geração dos relatórios devido a possíveis "
            "indisponibilidades nas fontes públicas.",
        ]

        for i, note in enumerate(notes, start=1):
            grey_fill = PatternFill(
                start_color="f2f2f2", end_color="f2f2f2", fill_type="solid"
            )
            index_cell = sheet.cell(row=i + 1, column=2, value=i)
            note_cell = sheet.cell(row=i + 1, column=3)
            index_cell.font = content_font
            note_cell.font = content_font

            part1 = note.split(":")[0]
            part2 = note.split(":")[1]
            rich_text = CellRichText(
                TextBlock(InlineFont(b=True), part1),
                ":",
                part2,
            )
            note_cell.value = rich_text

            if i % 2 != 0:
                index_cell.fill = grey_fill
                note_cell.fill = grey_fill

        for col in sheet.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            adjusted_width = max_length + 2
            sheet.column_dimensions[column].width = adjusted_width

        workbook.save(output_local_file)
        workbook.close()

    def write_data_not_evaluated(self, output_local_file, cars_not_evaluated):
        workbook = load_workbook(output_local_file)
        sheet = workbook.create_sheet("Dados Não Avaliados")
        sheet.sheet_view.showGridLines = False

        roboto_font = Font(name="Roboto", size=11, bold=False)
        roboto_bold_font = Font(name="Roboto", size=11, bold=True)
        center_align = Alignment(horizontal="center", vertical="center")
        thin_border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin"),
        )
        header_fill = PatternFill(
            start_color="D9D9D9", end_color="D9D9D9", fill_type="solid"
        )

        for row_idx, row in enumerate(
            dataframe_to_rows(cars_not_evaluated, index=False, header=True), start=3
        ):
            for col_idx, value in enumerate(row, start=2):
                cell = sheet.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border

                if row_idx == 3:
                    cell.font = roboto_bold_font
                    cell.fill = header_fill
                    cell.alignment = center_align
                else:
                    cell.font = roboto_font

        sheet.column_dimensions["B"].width = 20
        sheet.column_dimensions["C"].width = 60

        workbook.save(output_local_file)
        workbook.close()
